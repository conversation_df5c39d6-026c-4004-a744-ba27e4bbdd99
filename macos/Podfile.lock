PODS:
  - file_picker (0.0.1):
    - FlutterMacOS
  - flutter_inappwebview_macos (0.0.1):
    - FlutterMacOS
    - OrderedSet (~> 6.0.3)
  - FlutterMacOS (1.0.0)
  - mobile_scanner (7.0.0):
    - Flutter
    - FlutterMacOS
  - network_info_plus (0.0.1):
    - FlutterMacOS
  - OrderedSet (6.0.3)
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - screen_retriever_macos (0.0.1):
    - FlutterMacOS
  - share_plus (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - window_manager (0.5.0):
    - FlutterMacOS

DEPENDENCIES:
  - file_picker (from `Flutter/ephemeral/.symlinks/plugins/file_picker/macos`)
  - flutter_inappwebview_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_inappwebview_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - mobile_scanner (from `Flutter/ephemeral/.symlinks/plugins/mobile_scanner/darwin`)
  - network_info_plus (from `Flutter/ephemeral/.symlinks/plugins/network_info_plus/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - screen_retriever_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - window_manager (from `Flutter/ephemeral/.symlinks/plugins/window_manager/macos`)

SPEC REPOS:
  trunk:
    - OrderedSet

EXTERNAL SOURCES:
  file_picker:
    :path: Flutter/ephemeral/.symlinks/plugins/file_picker/macos
  flutter_inappwebview_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_inappwebview_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  mobile_scanner:
    :path: Flutter/ephemeral/.symlinks/plugins/mobile_scanner/darwin
  network_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/network_info_plus/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  screen_retriever_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_retriever_macos/macos
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  window_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/window_manager/macos

SPEC CHECKSUMS:
  file_picker: 7584aae6fa07a041af2b36a2655122d42f578c1a
  flutter_inappwebview_macos: c2d68649f9f8f1831bfcd98d73fd6256366d9d1d
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  mobile_scanner: 9157936403f5a0644ca3779a38ff8404c5434a93
  network_info_plus: f52abf61c7a311ecf3ac1b482be8a28908b4604d
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: f0052d280d17aa382b932f399edf32507174e870
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  screen_retriever_macos: 452e51764a9e1cdb74b3c541238795849f21557f
  share_plus: 510bf0af1a42cd602274b4629920c9649c52f4cc
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  url_launcher_macos: 0fba8ddabfc33ce0a9afe7c5fef5aab3d8d2d673
  window_manager: b729e31d38fb04905235df9ea896128991cad99e

PODFILE CHECKSUM: 236401fc2c932af29a9fcf0e97baeeb2d750d367

COCOAPODS: 1.16.2
