# Fastforge 配置文件
# 参考官方示例: https://github.com/fastforgedev/fastforge
# 使用命令: fastforge release --name=dev

output: dist/
releases:
  - name: linux 
    jobs:
      # Linux DEB 包构建 (Debian/Ubuntu)
      - name: release-linux-deb
        package:
          platform: linux
          target: deb
          build_args:
            dart-define:
              APP_ENV: production
      
      # Linux RPM 包构建 (Red Hat/CentOS/Fedora)
      - name: release-linux-rpm
        package:
          platform: linux
          target: rpm
          build_args:
            dart-define:
              APP_ENV: production
      
      # Linux AppImage 包构建 (便携式)
      - name: release-linux-appimage
        package:
          platform: linux
          target: appimage
          build_args:
            dart-define:
              APP_ENV: production
      
  - name: android
    jobs:
      # Android APK 构建
      - name: release-android-apk
        package:
          platform: android
          target: apk
          build_args:
            target-platform: android-arm,android-arm64
            dart-define:
              APP_ENV: production
              
  - name: windows
    jobs:
      # Windows ZIP 包构建
      - name: release-windows-zip
        package:
          platform: windows
          target: zip
          build_args:
            dart-define:
              APP_ENV: production
          # 添加额外文件配置
          extra_files:
            - source: "windows/sqlite3.dll"
              destination: "sqlite3.dll"
      - name: release-windows-msix
        package:
          platform: windows
          target: msix
          build_args:
            dart-define:
              APP_ENV: production
              
  - name: macos
    jobs:
      # macOS DMG 构建
      - name: release-macos-dmg
        package:
          platform: macos
          target: dmg
          build_args:
            dart-define:
              APP_ENV: production
  
  - name: web
    jobs:
      # Web 构建
      - name: release-web-tarball
        package:
          platform: web
          target: tar.gz
          build_args:
            dart-define:
              APP_ENV: production
