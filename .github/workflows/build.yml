name: Build and Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

jobs:
  build:
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        include:
          - os: ubuntu-latest
            apk_build: true
          - os: macos-latest
            apk_build: false
          - os: windows-latest
            apk_build: false
    runs-on: ${{ matrix.os }}

    steps:
      - uses: actions/checkout@v3

      - name: Setup Java
        if: matrix.apk_build
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: '17'

      - name: Setup Android SDK
        if: matrix.apk_build
        uses: android-actions/setup-android@v3

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          channel: 'stable'
          flutter-version: '3.32.5'

      - name: Create empty .env file
        run: touch .env

      - name: Install dependencies
        run: flutter pub get

      - name: Setup Android Signing
        if: matrix.apk_build && github.event_name != 'pull_request'
        env:
          SIGNING_KEYSTORE: ${{ secrets.SIGNING_KEYSTORE }}
          SIGNING_KEY_ALIAS: ${{ secrets.SIGNING_KEY_ALIAS }}
          SIGNING_STORE_PASSWORD: ${{ secrets.SIGNING_STORE_PASSWORD }}
          SIGNING_KEY_PASSWORD: ${{ secrets.SIGNING_KEY_PASSWORD }}
        run: |
          mkdir -p android/app/keystore
          
          if [ -n "$SIGNING_KEYSTORE" ]; then
            echo "$SIGNING_KEYSTORE" | base64 -d > android/app/keystore/release.jks
            echo "SIGNING_STORE_PATH=keystore/release.jks" >> $GITHUB_ENV
            echo "SIGNING_KEY_ALIAS=$SIGNING_KEY_ALIAS" >> $GITHUB_ENV
            echo "SIGNING_STORE_PASSWORD=$SIGNING_STORE_PASSWORD" >> $GITHUB_ENV
            echo "SIGNING_KEY_PASSWORD=$SIGNING_KEY_PASSWORD" >> $GITHUB_ENV
            echo "✅ Android 签名配置已设置"
          else
            echo "⚠️  未找到签名配置，将使用 debug 签名"
          fi
      
      - name: Activate FastForge
        run: dart pub global activate fastforge

      - name: Build linux/android package
        if: matrix.os == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y ninja-build libgtk-3-dev locate libsqlite3-dev fuse libfuse2
          sudo updatedb
          wget -O appimagetool "https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage"
          chmod +x appimagetool
          sudo mv appimagetool /usr/local/bin/
          fastforge release --name=linux
          fastforge release --name=android

      - name: Setup Node.js for macOS
        if: matrix.os == 'macos-latest'
        uses: actions/setup-node@v3
        with:
          node-version: '20'

      - name: macos npm install
        if: matrix.os == 'macos-latest'
        run: |
          npm install -g appdmg
        
      - name: Build macos package
        if: matrix.os == 'macos-latest'
        run: |
          fastforge release --name=macos

      - name: Build windows package
        if: matrix.os == 'windows-latest'
        run: |
          fastforge release --name=windows
      
      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: build-${{ matrix.os }}
          path: |
            dist/*/*

      - name: Create Release
        if: startsWith(github.ref, 'refs/tags/')
        uses: softprops/action-gh-release@v1
        with:
          files: |
            dist/*/*
        env:
          GITHUB_TOKEN: ${{ secrets.MY_GITHUB_TOKEN }}