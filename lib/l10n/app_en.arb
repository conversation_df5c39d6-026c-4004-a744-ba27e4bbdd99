{"@@locale": "en", "settings": "Settings", "general": "General", "providers": "Providers", "mcpServer": "MCP Server", "language": "Language", "theme": "Theme", "dark": "Dark", "light": "Light", "system": "System", "languageSettings": "Language Settings", "featureSettings": "Feature Settings", "enableArtifacts": "Enable Artifacts", "enableArtifactsDescription": "Enable the artifacts of the AI assistant in the conversation, will use more tokens", "enableToolUsage": "Enable Tool Usage", "enableToolUsageDescription": "Enable the usage of tools in the conversation, will use more tokens", "themeSettings": "Theme Settings", "lightTheme": "Light Theme", "darkTheme": "Dark Theme", "followSystem": "Follow System", "showAvatar": "Show Avatar", "showAssistantAvatar": "Show Assistant <PERSON><PERSON>", "showAssistantAvatarDescription": "Show the avatar of the AI assistant in the conversation", "showUserAvatar": "Show User Avatar", "showUserAvatarDescription": "Show the avatar of the user in the conversation", "systemPrompt": "System Prompt", "systemPromptDescription": "This is the system prompt for the conversation with the AI assistant, used to set the behavior and style of the assistant", "llmKey": "LLM Key", "toolKey": "Tool Key", "saveSettings": "Save Settings", "apiKey": "API Key", "enterApiKey": "Enter your {provider} API Key", "apiKeyValidation": "API Key must be at least 10 characters", "apiEndpoint": "API Endpoint", "enterApiEndpoint": "Enter API endpoint URL", "apiVersion": "API Version", "enterApiVersion": "Enter API Version", "platformNotSupported": "Current platform does not support MCP Server", "mcpServerDesktopOnly": "MCP Server only supports desktop platforms (Windows, macOS, Linux)", "searchServer": "Search server...", "noServerConfigs": "No server configurations found", "addProvider": "Add Provider", "refresh": "Refresh", "install": "Install", "edit": "Edit", "delete": "Delete", "command": "Command Or Sever Url", "arguments": "Arguments", "environmentVariables": "Environment Variables", "serverName": "Server Name", "commandExample": "For example: npx, uvx, https://mcpserver.com", "argumentsExample": "Separate arguments with spaces, use quotes for arguments with spaces, for example: -y obsidian-mcp '/Users/<USER>/Documents/Obsidian Vault'", "envVarsFormat": "One per line, format: KEY=VALUE", "cancel": "Cancel", "save": "Save", "confirmDelete": "Confirm Delete", "confirmDeleteServer": "Are you sure you want to delete server \"{name}\" ?", "error": "Error", "commandNotExist": "Command \"{command}\" does not exist, please install it first\n\nCurrent PATH:\n{path}", "all": "All", "installed": "Installed", "modelSettings": "Model Settings", "temperature": "Temperature: {value}", "temperatureTooltip": "Sampling temperature controls the randomness of output:\n• 0.0: Suitable for code generation and math problems\n• 1.0: Suitable for data extraction and analysis\n• 1.3: Suitable for general conversation and translation\n• 1.5: Suitable for creative writing and poetry", "topP": "Top P: {value}", "topPTooltip": "Top P (nucleus sampling) is an alternative to temperature. The model only considers tokens whose cumulative probability exceeds P. It is recommended not to modify both temperature and top_p at the same time.", "maxTokens": "<PERSON>", "maxTokensTooltip": "Maximum number of tokens to generate. One token is approximately equal to 4 characters. Longer conversations require more tokens.", "frequencyPenalty": "Frequency Penalty: {value}", "frequencyPenaltyTooltip": "Frequency penalty parameter. Positive values penalize new tokens based on their existing frequency in the text, decreasing the model's likelihood of repeating the same content verbatim.", "presencePenalty": "Presence Penalty: {value}", "presencePenaltyTooltip": "Presence penalty parameter. Positive values penalize new tokens based on whether they appear in the text, increasing the model's likelihood of talking about new topics.", "enterMaxTokens": "Enter max tokens", "share": "Share", "modelConfig": "Model Config", "debug": "Debug", "webSearchTest": "Web Search Test", "today": "Today", "yesterday": "Yesterday", "last7Days": "Last 7 Days", "last30Days": "Last 30 Days", "earlier": "Earlier", "confirmDeleteSelected": "Are you sure you want to delete the selected conversations?", "confirmThisChat": "Are you sure you want to delete the this conversations", "ok": "OK", "askMeAnything": "Ask me anything...", "uploadFiles": "Upload Files", "welcomeMessage": "How can I help you today?", "copy": "Copy", "copied": "Copied to clipboard", "retry": "Retry", "brokenImage": "Broken Image", "toolCall": "call {name}", "toolResult": "call {name} result", "selectModel": "Select Model", "close": "Close", "selectFromGallery": "Select from Gallery", "selectFile": "Select File", "uploadFile": "Upload File", "openBrowser": "Open Browser", "codeCopiedToClipboard": "Code copied to clipboard", "thinking": "Thinking", "thinkingEnd": "Thinking End", "tool": "Tool", "userCancelledToolCall": "Tool execution failed", "code": "Code", "preview": "Preview", "loadContentFailed": "Failed to load content, please retry", "openingBrowser": "Opening browser", "functionCallAuth": "Tool Call Authorization", "allowFunctionExecution": "Do you want to allow the following tool to execute:", "parameters": "Parameters: {params}", "allow": "Allow", "loadDiagramFailed": "Failed to load diagram, please retry", "copiedToClipboard": "Copied to clipboard", "chinese": "Chinese", "turkish": "Turkish", "functionRunning": "Running Tool...", "thinkingProcess": "Thinking", "thinkingProcessWithDuration": "Thinking, time used", "thinkingEndWithDuration": "Thinking finished, time used", "thinkingEndComplete": "Thinking finished", "seconds": "{seconds}s", "fieldRequired": "This field is required", "autoApprove": "Auto Approve", "verify": "Verify Key", "howToGet": "How to get", "modelList": "Model List", "enableModels": "Enable Models", "disableAllModels": "Disable All Models", "saveSuccess": "Setting<PERSON> saved successfully", "genTitleModel": "Gen Title", "serverNameTooLong": "Server name cannot exceed 50 characters", "confirm": "Confirm", "providerName": "Provider Name", "apiStyle": "API Style", "enterProviderName": "Enter provider name", "providerNameRequired": "Provider name is required", "addModel": "Add Model", "modelName": "Model Name", "enterModelName": "Enter model name", "noApiConfigs": "No API configurations available", "add": "Add", "fetch": "<PERSON>tch", "on": "ON", "off": "OFF", "apiUrl": "API URL", "selectApiStyle": "Please select API style", "serverType": "Server Type", "reset": "Reset", "start": "Start", "stop": "stop", "search": "Search", "newVersionFound": "New version {version} available", "newVersionAvailable": "New version available", "updateNow": "Update Now", "updateLater": "Update Later", "ignoreThisVersion": "Ignore This Version", "releaseNotes": "Release Notes:", "openUrlFailed": "Failed to open URL", "checkingForUpdates": "Checking for updates...", "checkUpdate": "Update Check", "appDescription": "ChatMCP is a cross-platform AI client, dedicated to making AI accessible to more people.", "visitWebsite": "website", "aboutApp": "About", "networkError": "Network error occurred", "noElementError": "No element found", "permissionError": "Permission denied", "unknownError": "Unknown error occurred", "timeoutError": "Request timed out", "notFoundError": "Resource not found", "invalidError": "Invalid request", "unauthorizedError": "Unauthorized access", "minimize": "Minimize", "maximize": "Maximize", "conversationSettings": "Conversation Settings", "maxMessages": "Max Messages", "maxMessagesDescription": "Limit the maximum number of messages passed to LLM (1-1000)", "maxLoops": "Max Loops", "maxLoopsDescription": "Limit the maximum number of tool call loops to prevent infinite loops (1-1000)", "mcpServers": "MCP Servers", "getApiKey": "Get API Key", "proxySettings": "Proxy Settings", "enableProxy": "Enable Proxy", "enableProxyDescription": "When enabled, network requests will go through the configured proxy server", "proxyType": "Proxy Type", "proxyHost": "Proxy Host", "proxyPort": "Proxy Port", "proxyUsername": "Username", "proxyPassword": "Password", "enterProxyHost": "Enter proxy server address", "enterProxyPort": "Enter proxy port", "enterProxyUsername": "Enter username (optional)", "enterProxyPassword": "Enter password (optional)", "proxyHostRequired": "Proxy host is required", "proxyPortInvalid": "Proxy port must be between 1-65535", "saved": "Saved", "dataSync": "Data Sync", "syncServerRunning": "Sync server is running", "maintenance": "Maintenance", "cleanupLogs": "Cleanup Old Logs", "cleanupLogsDescription": "Cleanup log files", "confirmCleanup": "Confirm Cleanup", "confirmCleanupMessage": "Are you sure you want to delete log files? This action cannot be undone.", "cleanupSuccess": "Old logs cleanup completed", "cleanupFailed": "Cleanup failed", "syncServerStopped": "Sync server stopped", "scanQRToConnect": "Other devices scan this QR code to connect:", "addressCopied": "Address copied to clipboard", "otherDevicesCanScan": "Other devices can scan this QR code to connect quickly", "startServer": "Start Server", "stopServer": "Stop Server", "connectToOtherDevices": "Connect to Other Devices", "scanQRCode": "Scan QR Code to Connect", "connectionHistory": "Connection History:", "connect": "Connect", "manualInputAddress": "Or manually enter server address:", "serverAddress": "Server Address", "syncFromServer": "Sync from Server", "pushToServer": "Push to <PERSON>", "usageInstructions": "Usage Instructions", "desktopAsServer": "Desktop as Server:", "desktopStep1": "1. Click \"Start Server\" button", "desktopStep2": "2. Show QR code for mobile to scan", "desktopStep3": "3. Mobile can sync data after scanning", "mobileConnect": "Mobile Connection:", "mobileStep1": "1. <PERSON><PERSON> \"Scan QR Code to Connect\"", "mobileStep2": "2. <PERSON>an the QR code displayed on desktop", "mobileStep3": "3. Choose sync direction (upload/download)", "uploadDescription": "• Upload: Push local device data to server", "downloadDescription": "• Download: Get data from server to local device", "syncContent": "• Sync Content: Chat history, settings, MCP configs", "syncServerStarted": "Sync server started", "syncServerStartFailed": "Failed to start server", "syncServerStopFailed": "Failed to stop server", "scanQRCodeTitle": "Scan QR Code", "flashOn": "Flash On", "flashOff": "Flash Off", "aimQRCode": "Aim the QR code at the scanning frame", "scanSyncQRCode": "Scan the sync QR code displayed on desktop", "manualInputAddressButton": "Manual Input Address", "manualInputServerAddress": "Manually Input Server Address", "enterValidServerAddress": "Please enter a valid server address", "scanSuccessConnectTo": "<PERSON>an successful, connected to: {deviceName}", "scanSuccessAddressFilled": "Scan successful, server address filled", "scannerOpenFailed": "Failed to open scanner", "pleaseInputServerAddress": "Please scan QR code or input server address first", "connectingToServer": "Connecting to server...", "downloadingData": "Downloading data...", "importingData": "Importing data...", "reinitializingData": "Reinitializing app data...", "dataSyncSuccess": "Data sync successful", "preparingData": "Preparing data...", "uploadingData": "Uploading data...", "dataPushSuccess": "Data push successful", "syncFailed": "Sync failed", "pushFailed": "Push failed", "justNow": "Just now", "minutesAgo": "{minutes} minutes ago", "hoursAgo": "{hours} hours ago", "daysAgo": "{days} days ago", "serverSelected": "Server selected: {deviceName}", "connectionRecordDeleted": "Connection record deleted", "viewAllConnections": "View all {count} connections", "clearAllHistory": "Clear All", "clearAllConnectionHistory": "All connection history cleared", "unknownDevice": "Unknown Device", "unknownPlatform": "Unknown Platform", "inmemory": "In Memory", "toggleSidebar": "Toggle Sidebar", "deleteChat": "Delete Chat", "selectAll": "Select All", "newChat": "New Chat", "send": "Send", "more": "More", "noMoreData": "No more data"}