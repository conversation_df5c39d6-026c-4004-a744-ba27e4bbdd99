{"@@locale": "zh", "settings": "设置", "general": "通用", "providers": "服务提供商", "mcpServer": "MCP 服务器", "language": "语言", "theme": "主题", "dark": "深色", "light": "浅色", "system": "跟随系统", "languageSettings": "语言设置", "featureSettings": "功能设置", "enableArtifacts": "启用人工制品", "enableArtifactsDescription": "在对话中启用 AI 助手的人工制品，这将使用更多的令牌", "enableToolUsage": "启用工具使用", "enableToolUsageDescription": "在对话中启用工具的使用，这将使用更多的令牌", "themeSettings": "主题设置", "lightTheme": "浅色主题", "darkTheme": "深色主题", "followSystem": "跟随系统", "showAvatar": "显示头像", "showAssistantAvatar": "显示助手头像", "showAssistantAvatarDescription": "在对话中显示 AI 助手的头像", "showUserAvatar": "显示用户头像", "showUserAvatarDescription": "在对话中显示用户的头像", "systemPrompt": "系统提示词", "systemPromptDescription": "这是与 AI 助手对话的系统提示词，用于设置助手的行为和风格", "llmKey": "LLM 密钥", "toolKey": "工具密钥", "saveSettings": "保存设置", "apiKey": "API 密钥", "enterApiKey": "请输入您的 {provider} API 密钥", "apiKeyValidation": "API 密钥必须至少包含 10 个字符", "apiEndpoint": "API 端点", "enterApiEndpoint": "请输入 API 端点 URL", "apiVersion": "API 版本", "enterApiVersion": "输入 API 版本", "platformNotSupported": "当前平台不支持 MCP Server", "mcpServerDesktopOnly": "MCP Server 仅支持桌面端（Windows、macOS、Linux）", "searchServer": "搜索服务器...", "noServerConfigs": "未找到服务器配置", "addProvider": "添加服务商", "refresh": "刷新", "install": "安装", "edit": "编辑", "delete": "删除", "command": "命令 或 服务地址", "arguments": "参数", "environmentVariables": "环境变量", "serverName": "服务器名称", "commandExample": "例如：npx、uvx、https://mcpserver.com", "argumentsExample": "参数之间用空格分隔，包含空格的参数请用引号包围，例如：-y obsidian-mcp '/Users/<USER>/Documents/Obsidian Vault'", "envVarsFormat": "每行一个，格式：KEY=VALUE", "cancel": "取消", "save": "保存", "confirmDelete": "确认删除", "confirmDeleteServer": "您确定要删除服务器 \"{name}\" 吗？", "error": "错误", "commandNotExist": "命令 \"{command}\" 不存在，请先安装\n\n当前 PATH：\n{path}", "all": "全部", "installed": "已安装", "modelSettings": "模型设置", "temperature": "采样温度: {value}", "temperatureTooltip": "采样温度控制输出的随机性：\n• 0.0：适合代码生成和数学解题\n• 1.0：适合数据抽取和分析\n• 1.3：适合通用对话和翻译\n• 1.5：适合创意写作和诗歌创作", "topP": "核采样: {value}", "topPTooltip": "Top P（核采样）是temperature的替代方案。模型只考虑累积概率超过P的标记。建议不要同时修改temperature和top_p。", "maxTokens": "最大令牌数", "maxTokensTooltip": "生成的最大令牌数。一个令牌大约等于4个字符。较长的对话需要更多的令牌。", "frequencyPenalty": "频率惩罚: {value}", "frequencyPenaltyTooltip": "频率惩罚参数。正值会根据新标记在文本中的现有频率来惩罚它们，降低模型逐字重复同样内容的可能性。", "presencePenalty": "存在惩罚: {value}", "presencePenaltyTooltip": "存在惩罚参数。正值会根据新标记是否出现在文本中来惩罚它们，增加模型谈论新主题的可能性。", "enterMaxTokens": "输入最大令牌数", "share": "分享", "modelConfig": "模型配置", "debug": "调试", "webSearchTest": "网页搜索测试", "today": "今天", "yesterday": "昨天", "last7Days": "前 7 天", "last30Days": "前 30 天", "earlier": "更早", "confirmDeleteSelected": "确定要删除选中的对话吗？", "confirmThisChat": "您确定要删除这些对话吗？", "ok": "确定", "askMeAnything": "问我任何问题...", "uploadFiles": "上传文件", "welcomeMessage": "今天我能帮您什么？", "copy": "复制", "copied": "已复制到剪贴板", "retry": "重试", "brokenImage": "图片损坏", "toolCall": "调用 {name}", "toolResult": "调用 {name} 结果", "selectModel": "选择模型", "close": "关闭", "selectFromGallery": "从图库选择", "selectFile": "选择文件", "uploadFile": "上传文件", "openBrowser": "打开浏览器", "codeCopiedToClipboard": "代码已复制到剪贴板", "thinking": "思考中", "thinkingEnd": "思考结束", "tool": "工具", "userCancelledToolCall": "工具执行失败", "code": "代码", "preview": "预览", "functionCallAuth": "工具调用授权", "allowFunctionExecution": "是否允许执行以下工具:", "parameters": "参数: {params}", "allow": "允许", "loadContentFailed": "加载内容失败，请重试", "loadDiagramFailed": "加载图表失败，请重试", "copiedToClipboard": "已复制到剪贴板", "chinese": "中文", "turkish": "土耳其语", "functionRunning": "正在执行工具...", "thinkingProcess": "思考中", "thinkingProcessWithDuration": "思考中, 用时", "thinkingEndWithDuration": "思考完成，用时", "thinkingEndComplete": "思考完成", "seconds": "{seconds}秒", "fieldRequired": "此字段为必填项", "autoApprove": "自动批准", "verify": "验证密钥", "howToGet": "如何获取", "modelList": "模型列表", "enableModels": "启用模型", "disableAllModels": "禁用所有模型", "saveSuccess": "设置保存成功", "genTitleModel": "标题生成", "serverNameTooLong": "服务名称长度不能超过 50 个字符", "serverType": "服务类型", "reset": "重置", "start": "启动", "stop": "停止", "search": "搜索", "newVersionFound": "发现新版本 {version}", "newVersionAvailable": "发现新版本", "updateNow": "立即更新", "updateLater": "稍后更新", "ignoreThisVersion": "忽略此版本", "releaseNotes": "更新内容：", "openUrlFailed": "无法打开链接", "checkingForUpdates": "正在检查更新...", "checkUpdate": "检查更新", "appDescription": "ChatMCP 是一款跨平台的 AI 客户端，致力于让更多人方便地使用 AI。", "visitWebsite": "站点", "aboutApp": "关于", "networkError": "网络连接问题，请检查网络后重试", "noElementError": "未找到匹配内容，请重试", "permissionError": "权限不足，请检查设置", "unknownError": "发生未知错误", "timeoutError": "请求超时，请稍后重试", "notFoundError": "未找到请求的资源", "invalidError": "无效的请求或参数", "unauthorizedError": "未授权访问，请检查权限", "minimize": "最小化", "maximize": "最大化", "conversationSettings": "对话设置", "maxMessages": "最大消息数", "maxMessagesDescription": "限制传递给 LLM 的最大消息数量 (1-1000)", "maxLoops": "最大循环数", "maxLoopsDescription": "限制工具调用的最大循环次数，防止无限循环 (1-1000)", "mcpServers": "MCP 服务器", "getApiKey": "获取 API 秘钥", "openingBrowser": "正在打开浏览器", "confirm": "确认", "providerName": "提供商名称", "apiStyle": "API 风格", "enterProviderName": "输入提供商名称", "providerNameRequired": "提供商名称为必填项", "addModel": "添加模型", "modelName": "模型名称", "enterModelName": "输入模型名称", "noApiConfigs": "无可用的 API 配置", "add": "添加", "fetch": "获取", "on": "开启", "off": "关闭", "apiUrl": "API 地址", "selectApiStyle": "请选择 API 风格", "proxySettings": "代理设置", "enableProxy": "启用代理", "enableProxyDescription": "启用代理后，网络请求将通过配置的代理服务器", "proxyType": "代理类型", "proxyHost": "代理地址", "proxyPort": "代理端口", "proxyUsername": "用户名", "proxyPassword": "密码", "enterProxyHost": "请输入代理服务器地址", "enterProxyPort": "请输入代理端口", "enterProxyUsername": "请输入用户名（可选）", "enterProxyPassword": "请输入密码（可选）", "proxyHostRequired": "代理地址为必填项", "proxyPortInvalid": "代理端口必须在 1-65535 之间", "saved": "已保存", "dataSync": "数据同步", "syncServerRunning": "同步服务器运行中", "maintenance": "维护", "cleanupLogs": "清理旧日志", "cleanupLogsDescription": "清理日志文件", "confirmCleanup": "确认清理", "confirmCleanupMessage": "确定要删除日志文件吗？此操作不可撤销。", "cleanupSuccess": "旧日志清理完成", "cleanupFailed": "清理失败", "syncServerStopped": "同步服务器已停止", "scanQRToConnect": "其他设备扫描此二维码连接：", "addressCopied": "地址已复制到剪贴板", "otherDevicesCanScan": "其他设备可以扫描此二维码快速连接", "startServer": "启动服务器", "stopServer": "停止服务器", "connectToOtherDevices": "连接到其他设备", "scanQRCode": "扫描二维码连接", "connectionHistory": "连接历史：", "connect": "连接", "manualInputAddress": "或手动输入服务器地址：", "serverAddress": "服务器地址", "syncFromServer": "从服务器同步", "pushToServer": "推送到服务器", "usageInstructions": "使用说明", "desktopAsServer": "桌面端作为服务器：", "desktopStep1": "1. 点击\"启动服务器\"按钮", "desktopStep2": "2. 显示二维码给手机扫描", "desktopStep3": "3. 手机扫码后可进行数据同步", "mobileConnect": "手机端连接：", "mobileStep1": "1. 点击\"扫描二维码连接\"", "mobileStep2": "2. 扫描桌面端显示的二维码", "mobileStep3": "3. 选择同步方向（上传/下载）", "uploadDescription": "• 上传：将本设备数据推送到服务器", "downloadDescription": "• 下载：从服务器获取数据到本设备", "syncContent": "• 同步内容：聊天记录、设置、MCP配置", "syncServerStarted": "同步服务器已启动", "syncServerStartFailed": "启动服务器失败", "syncServerStopFailed": "停止服务器失败", "scanQRCodeTitle": "扫描二维码", "flashOn": "开启闪光灯", "flashOff": "关闭闪光灯", "aimQRCode": "将二维码对准扫描框", "scanSyncQRCode": "扫描桌面端显示的同步二维码", "manualInputAddressButton": "手动输入地址", "manualInputServerAddress": "手动输入服务器地址", "enterValidServerAddress": "请输入有效的服务器地址", "scanSuccessConnectTo": "扫描成功，连接到: {deviceName}", "scanSuccessAddressFilled": "扫描成功，已填入服务器地址", "scannerOpenFailed": "打开扫码器失败", "pleaseInputServerAddress": "请先扫码或输入服务器地址", "connectingToServer": "正在连接服务器...", "downloadingData": "正在下载数据...", "importingData": "正在导入数据...", "reinitializingData": "正在重新初始化应用数据...", "dataSyncSuccess": "数据同步成功", "preparingData": "正在准备数据...", "uploadingData": "正在上传数据...", "dataPushSuccess": "数据推送成功", "syncFailed": "同步失败", "pushFailed": "推送失败", "justNow": "刚刚", "minutesAgo": "{minutes}分钟前", "hoursAgo": "{hours}小时前", "daysAgo": "{days}天前", "serverSelected": "已选择服务器: {deviceName}", "connectionRecordDeleted": "已删除连接记录", "viewAllConnections": "查看全部 {count} 个连接", "clearAllHistory": "清空所有", "clearAllConnectionHistory": "已清空所有连接历史", "unknownDevice": "未知设备", "unknownPlatform": "未知平台", "inmemory": "内置", "toggleSidebar": "切换侧边栏", "deleteChat": "删除聊天", "selectAll": "全选", "newChat": "新聊天", "send": "发送", "more": "更多", "noMoreData": "没有更多了"}